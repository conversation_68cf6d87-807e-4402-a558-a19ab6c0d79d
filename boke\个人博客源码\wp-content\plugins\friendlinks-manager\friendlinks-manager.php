<?php
/**
 * Plugin Name: 友链管理器
 * Plugin URI: https://example.com
 * Description: 一个简单易用的友链管理插件，支持卡片式展示
 * Version: 1.0.0
 * Author: 友链管理器
 * License: GPL v2 or later
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 定义插件常量
define('FRIENDLINKS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('FRIENDLINKS_PLUGIN_PATH', plugin_dir_path(__FILE__));

// 激活插件时创建数据库表和友链页面
register_activation_hook(__FILE__, 'friendlinks_create_table');

function friendlinks_create_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'friendlinks';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        name varchar(100) NOT NULL,
        url varchar(255) NOT NULL,
        description text,
        avatar varchar(255),
        status tinyint(1) DEFAULT 1,
        sort_order int(11) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // 创建友链页面
    friendlinks_create_page();
}

// 创建友链页面
function friendlinks_create_page() {
    // 检查是否已存在友链页面
    $existing_page = get_page_by_path('friendlinks');

    if ($existing_page) {
        return $existing_page->ID;
    }

    // 创建友链页面
    $page_data = array(
        'post_title'    => '友情链接',
        'post_content'  => '
<div class="friendlinks-intro">
    <p>欢迎来到友情链接页面！这里展示了与本站互相链接的优秀网站。</p>
    <p>如果您希望与本站交换友链，请联系站长。</p>
</div>

[friendlinks]

<div class="friendlinks-apply">
    <h3>申请友链</h3>
    <p>如果您想申请友链，请确保您的网站符合以下要求：</p>
    <ul>
        <li>网站内容健康，无违法违规内容</li>
        <li>网站能够正常访问</li>
        <li>网站有一定的更新频率</li>
        <li>优先考虑技术、生活、学习类博客</li>
    </ul>
    <p>申请时请提供：网站名称、网站地址、网站描述、网站头像（可选）</p>
</div>
        ',
        'post_status'   => 'publish',
        'post_type'     => 'page',
        'post_name'     => 'friendlinks',
        'page_template' => 'page-friendlinks.php'
    );

    $page_id = wp_insert_post($page_data);

    if ($page_id) {
        // 设置页面模板
        update_post_meta($page_id, '_wp_page_template', 'page-friendlinks.php');
        return $page_id;
    }

    return false;
}

// 添加管理菜单
add_action('admin_menu', 'friendlinks_admin_menu');

function friendlinks_admin_menu() {
    add_menu_page(
        '友链管理',
        '友链管理',
        'manage_options',
        'friendlinks-manager',
        'friendlinks_admin_page',
        'dashicons-admin-links',
        26
    );
    
    add_submenu_page(
        'friendlinks-manager',
        '所有友链',
        '所有友链',
        'manage_options',
        'friendlinks-manager',
        'friendlinks_admin_page'
    );
    
    add_submenu_page(
        'friendlinks-manager',
        '添加友链',
        '添加友链',
        'manage_options',
        'friendlinks-add',
        'friendlinks_add_page'
    );
}

// 加载管理页面样式
add_action('admin_enqueue_scripts', 'friendlinks_admin_styles');

function friendlinks_admin_styles($hook) {
    if (strpos($hook, 'friendlinks') !== false) {
        wp_enqueue_style('friendlinks-admin', FRIENDLINKS_PLUGIN_URL . 'admin-style.css');
        wp_enqueue_script('friendlinks-admin', FRIENDLINKS_PLUGIN_URL . 'admin-script.js', array('jquery'));
    }
}

// 主管理页面
function friendlinks_admin_page() {
    global $wpdb;
    
    // 处理删除操作
    if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
        $id = intval($_GET['id']);
        $wpdb->delete($wpdb->prefix . 'friendlinks', array('id' => $id));
        echo '<div class="notice notice-success"><p>友链已删除！</p></div>';
    }
    
    // 处理状态切换
    if (isset($_GET['action']) && $_GET['action'] == 'toggle' && isset($_GET['id'])) {
        $id = intval($_GET['id']);
        $current_status = $wpdb->get_var($wpdb->prepare("SELECT status FROM {$wpdb->prefix}friendlinks WHERE id = %d", $id));
        $new_status = $current_status ? 0 : 1;
        $wpdb->update($wpdb->prefix . 'friendlinks', array('status' => $new_status), array('id' => $id));
        echo '<div class="notice notice-success"><p>友链状态已更新！</p></div>';
    }
    
    $friendlinks = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}friendlinks ORDER BY sort_order ASC, id DESC");
    
    ?>
    <div class="wrap">
        <h1>友链管理 <a href="<?php echo admin_url('admin.php?page=friendlinks-add'); ?>" class="page-title-action">添加新友链</a></h1>
        
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>头像</th>
                    <th>名称</th>
                    <th>网址</th>
                    <th>描述</th>
                    <th>状态</th>
                    <th>排序</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($friendlinks)): ?>
                <tr>
                    <td colspan="7" style="text-align: center; padding: 20px;">
                        暂无友链，<a href="<?php echo admin_url('admin.php?page=friendlinks-add'); ?>">点击添加</a>
                    </td>
                </tr>
                <?php else: ?>
                <?php foreach ($friendlinks as $link): ?>
                <tr>
                    <td>
                        <?php if ($link->avatar): ?>
                        <img src="<?php echo esc_url($link->avatar); ?>" alt="<?php echo esc_attr($link->name); ?>" style="width: 40px; height: 40px; border-radius: 50%;">
                        <?php else: ?>
                        <div style="width: 40px; height: 40px; background: #ddd; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                            <?php echo substr($link->name, 0, 1); ?>
                        </div>
                        <?php endif; ?>
                    </td>
                    <td><strong><?php echo esc_html($link->name); ?></strong></td>
                    <td><a href="<?php echo esc_url($link->url); ?>" target="_blank"><?php echo esc_html($link->url); ?></a></td>
                    <td><?php echo esc_html(wp_trim_words($link->description, 10)); ?></td>
                    <td>
                        <span class="status-<?php echo $link->status ? 'active' : 'inactive'; ?>">
                            <?php echo $link->status ? '启用' : '禁用'; ?>
                        </span>
                    </td>
                    <td><?php echo intval($link->sort_order); ?></td>
                    <td>
                        <a href="<?php echo admin_url('admin.php?page=friendlinks-add&action=edit&id=' . $link->id); ?>">编辑</a> |
                        <a href="<?php echo admin_url('admin.php?page=friendlinks-manager&action=toggle&id=' . $link->id); ?>">
                            <?php echo $link->status ? '禁用' : '启用'; ?>
                        </a> |
                        <a href="<?php echo admin_url('admin.php?page=friendlinks-manager&action=delete&id=' . $link->id); ?>" 
                           onclick="return confirm('确定要删除这个友链吗？')">删除</a>
                    </td>
                </tr>
                <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
        
        <div style="margin-top: 20px;">
            <h3>使用说明</h3>
            <p>在页面或文章中使用短代码 <code>[friendlinks]</code> 来显示友链。</p>
            <p>也可以在主题文件中使用 <code>&lt;?php echo do_shortcode('[friendlinks]'); ?&gt;</code> 来显示友链。</p>
        </div>
    </div>
    <?php
}

// 添加/编辑友链页面
function friendlinks_add_page() {
    global $wpdb;
    
    $editing = false;
    $link_data = null;
    
    if (isset($_GET['action']) && $_GET['action'] == 'edit' && isset($_GET['id'])) {
        $editing = true;
        $link_data = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$wpdb->prefix}friendlinks WHERE id = %d", intval($_GET['id'])));
    }
    
    // 处理表单提交
    if (isset($_POST['submit'])) {
        $name = sanitize_text_field($_POST['name']);
        $url = esc_url_raw($_POST['url']);
        $description = sanitize_textarea_field($_POST['description']);
        $avatar = esc_url_raw($_POST['avatar']);
        $status = isset($_POST['status']) ? 1 : 0;
        $sort_order = intval($_POST['sort_order']);
        
        if ($editing && $link_data) {
            // 更新
            $result = $wpdb->update(
                $wpdb->prefix . 'friendlinks',
                array(
                    'name' => $name,
                    'url' => $url,
                    'description' => $description,
                    'avatar' => $avatar,
                    'status' => $status,
                    'sort_order' => $sort_order
                ),
                array('id' => $link_data->id)
            );
            
            if ($result !== false) {
                echo '<div class="notice notice-success"><p>友链已更新！</p></div>';
            }
        } else {
            // 添加
            $result = $wpdb->insert(
                $wpdb->prefix . 'friendlinks',
                array(
                    'name' => $name,
                    'url' => $url,
                    'description' => $description,
                    'avatar' => $avatar,
                    'status' => $status,
                    'sort_order' => $sort_order
                )
            );
            
            if ($result) {
                echo '<div class="notice notice-success"><p>友链已添加！</p></div>';
                // 清空表单
                $name = $url = $description = $avatar = '';
                $status = 1;
                $sort_order = 0;
            }
        }
    }
    
    ?>
    <div class="wrap">
        <h1><?php echo $editing ? '编辑友链' : '添加友链'; ?></h1>
        
        <form method="post" action="">
            <table class="form-table">
                <tr>
                    <th scope="row"><label for="name">友链名称</label></th>
                    <td><input type="text" id="name" name="name" value="<?php echo $editing ? esc_attr($link_data->name) : ''; ?>" class="regular-text" required></td>
                </tr>
                <tr>
                    <th scope="row"><label for="url">友链地址</label></th>
                    <td><input type="url" id="url" name="url" value="<?php echo $editing ? esc_attr($link_data->url) : ''; ?>" class="regular-text" required></td>
                </tr>
                <tr>
                    <th scope="row"><label for="description">友链描述</label></th>
                    <td><textarea id="description" name="description" rows="3" class="large-text"><?php echo $editing ? esc_textarea($link_data->description) : ''; ?></textarea></td>
                </tr>
                <tr>
                    <th scope="row"><label for="avatar">头像地址</label></th>
                    <td><input type="url" id="avatar" name="avatar" value="<?php echo $editing ? esc_attr($link_data->avatar) : ''; ?>" class="regular-text"></td>
                </tr>
                <tr>
                    <th scope="row">状态</th>
                    <td>
                        <label>
                            <input type="checkbox" name="status" value="1" <?php echo ($editing && $link_data->status) || (!$editing) ? 'checked' : ''; ?>>
                            启用
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="sort_order">排序</label></th>
                    <td><input type="number" id="sort_order" name="sort_order" value="<?php echo $editing ? intval($link_data->sort_order) : 0; ?>" class="small-text"> <span class="description">数字越小越靠前</span></td>
                </tr>
            </table>
            
            <?php submit_button($editing ? '更新友链' : '添加友链'); ?>
        </form>
        
        <a href="<?php echo admin_url('admin.php?page=friendlinks-manager'); ?>">&larr; 返回友链列表</a>
    </div>
    <?php
}

// 注册短代码
add_shortcode('friendlinks', 'friendlinks_shortcode');

function friendlinks_shortcode($atts) {
    global $wpdb;

    $atts = shortcode_atts(array(
        'limit' => -1,
        'status' => 1
    ), $atts);

    $limit_sql = $atts['limit'] > 0 ? "LIMIT " . intval($atts['limit']) : "";
    $friendlinks = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}friendlinks WHERE status = %d ORDER BY sort_order ASC, id DESC $limit_sql",
        $atts['status']
    ));

    if (empty($friendlinks)) {
        return '<p>暂无友链</p>';
    }

    ob_start();
    ?>
    <div class="friendlinks-container">
        <?php foreach ($friendlinks as $link): ?>
        <div class="friendlink-card">
            <div class="friendlink-avatar">
                <?php if ($link->avatar): ?>
                <img src="<?php echo esc_url($link->avatar); ?>" alt="<?php echo esc_attr($link->name); ?>">
                <?php else: ?>
                <div class="friendlink-avatar-placeholder">
                    <?php echo substr($link->name, 0, 1); ?>
                </div>
                <?php endif; ?>
            </div>
            <div class="friendlink-content">
                <h3 class="friendlink-name">
                    <a href="<?php echo esc_url($link->url); ?>" target="_blank" rel="noopener">
                        <?php echo esc_html($link->name); ?>
                    </a>
                </h3>
                <?php if ($link->description): ?>
                <p class="friendlink-description"><?php echo esc_html($link->description); ?></p>
                <?php endif; ?>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php
    return ob_get_clean();
}

// 加载前端样式
add_action('wp_enqueue_scripts', 'friendlinks_frontend_styles');

function friendlinks_frontend_styles() {
    wp_enqueue_style('friendlinks-frontend', FRIENDLINKS_PLUGIN_URL . 'frontend-style.css');
}
