<?php
	$emotionListDefault = array(
		array(
			'groupname' => __('颜文字', 'argon'), 
			'list' => array(
				array('type' => 'text', 'text' => "|´・ω・)ノ"),
				array('type' => 'text', 'text' => "ヾ(≧∇≦*)ゝ"),
				array('type' => 'text', 'text' => "(☆ω☆)"),
				array('type' => 'text', 'text' => "（╯‵□′）╯︵┴─┴"),
				array('type' => 'text', 'text' => "￣﹃￣"),
				array('type' => 'text', 'text' => "(/ω＼)"),
				array('type' => 'text', 'text' => "∠( ᐛ 」∠)＿"),
				array('type' => 'text', 'text' => "(๑•̀ㅁ•́ฅ)"),
				array('type' => 'text', 'text' => "→_→"),
				array('type' => 'text', 'text' => "୧(๑•̀⌄•́๑)૭"),
				array('type' => 'text', 'text' => "٩(ˊᗜˋ*)و"),
				array('type' => 'text', 'text' => "(ノ°ο°)ノ"),
				array('type' => 'text', 'text' => "(´இ皿இ｀)"),
				array('type' => 'text', 'text' => "⌇●﹏●⌇"),
				array('type' => 'text', 'text' => "(ฅ´ω`ฅ)"),
				array('type' => 'text', 'text' => "(╯°A°)╯︵○○○"),
				array('type' => 'text', 'text' => "φ(￣∇￣o)"),
				array('type' => 'text', 'text' => "ヾ(´･ ･｀｡)ノ\""),
				array('type' => 'text', 'text' => "( ง ᵒ̌皿ᵒ̌)ง⁼³₌₃"),
				array('type' => 'text', 'text' => "(ó﹏ò｡)"),
				array('type' => 'text', 'text' => "Σ(っ °Д °;)っ"),
				array('type' => 'text', 'text' => "( ,,´･ω･)ﾉ\"(´っω･｀｡)"),
				array('type' => 'text', 'text' => "╮(╯▽╰)╭"),
				array('type' => 'text', 'text' => "o(*////▽////*)q"),
				array('type' => 'text', 'text' => "＞﹏＜"),
				array('type' => 'text', 'text' => "( ๑´•ω•) \"(ㆆᴗㆆ)")
			)
		),
		array(
			'groupname' => 'Emoji', 
			'list' => array(
				array('type' => 'text', 'text' => "😂"),
				array('type' => 'text', 'text' => "😀"),
				array('type' => 'text', 'text' => "😅"),
				array('type' => 'text', 'text' => "😊"),
				array('type' => 'text', 'text' => "🙂"),
				array('type' => 'text', 'text' => "🙃"),
				array('type' => 'text', 'text' => "😌"),
				array('type' => 'text', 'text' => "😍"),
				array('type' => 'text', 'text' => "😘"),
				array('type' => 'text', 'text' => "😜"),
				array('type' => 'text', 'text' => "😝"),
				array('type' => 'text', 'text' => "😏"),
				array('type' => 'text', 'text' => "😒"),
				array('type' => 'text', 'text' => "🙄"),
				array('type' => 'text', 'text' => "😳"),
				array('type' => 'text', 'text' => "😡"),
				array('type' => 'text', 'text' => "😔"),
				array('type' => 'text', 'text' => "😫"),
				array('type' => 'text', 'text' => "😱"),
				array('type' => 'text', 'text' => "😭"),
				array('type' => 'text', 'text' => "💩"),
				array('type' => 'text', 'text' => "👻"),
				array('type' => 'text', 'text' => "🙌"),
				array('type' => 'text', 'text' => "🖕"),
				array('type' => 'text', 'text' => "👍"),
				array('type' => 'text', 'text' => "👫"),
				array('type' => 'text', 'text' => "👬"),
				array('type' => 'text', 'text' => "👭"),
				array('type' => 'text', 'text' => "🌚"),
				array('type' => 'text', 'text' => "🌝"),
				array('type' => 'text', 'text' => "🙈"),
				array('type' => 'text', 'text' => "💊"),
				array('type' => 'text', 'text' => "😶"),
				array('type' => 'text', 'text' => "🙏"),
				array('type' => 'text', 'text' => "🍦"),
				array('type' => 'text', 'text' => "🍉"),
				array('type' => 'text', 'text' => "😣")
			)
		),
		array(
			'groupname' => '小恐龙', 
			'list' => array(
				array('type' => 'sticker', 'code' => 'dinosaur-shy', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/1.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-daze', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/2.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-sweat', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/3.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-proud', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/4.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-powerless', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/5.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-pouting', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/6.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-eating', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/7.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-ok', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/8.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-doubt', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/9.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-depressed', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/10.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-close-eyes', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/11.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-sleeping', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/12.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-puzzled', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/13.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-agree', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/14.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-crazy', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/15.jpg'),
				array('type' => 'sticker', 'code' => 'dinosaur-angry', 'src' => $GLOBALS['assets_path'] . '/stickers/dinosaur/16.jpg')
			)
		),
		array(
			'groupname' => '花!', 
			'list' => array(
				array('type' => 'sticker', 'code' => 'flower-flower', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/1.jpg'),
				array('type' => 'sticker', 'code' => 'flower-grass', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/2.jpg'),
				array('type' => 'sticker', 'code' => 'flower-leaf', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/3.jpg'),
				array('type' => 'sticker', 'code' => 'flower-star', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/4.jpg'),
				array('type' => 'sticker', 'code' => 'flower-sun', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/5.jpg'),
				array('type' => 'sticker', 'code' => 'flower-moon', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/6.jpg'),
				array('type' => 'sticker', 'code' => 'flower-water', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/7.jpg'),
				array('type' => 'sticker', 'code' => 'flower-heihei', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/8.jpg'),
				array('type' => 'sticker', 'code' => 'flower-lemon', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/9.jpg'),
				array('type' => 'sticker', 'code' => 'flower-birthday', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/10.jpg'),
				array('type' => 'sticker', 'code' => 'flower-sea', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/11.jpg'),
				array('type' => 'sticker', 'code' => 'flower-vegetable', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/12.jpg'),
				array('type' => 'sticker', 'code' => 'flower-tile', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/13.jpg'),
				array('type' => 'sticker', 'code' => 'flower-utf', 'src' => $GLOBALS['assets_path'] . '/stickers/flower/14.jpg'),
			),
			'description' => 'Source: github.com/k4yt3x/flowerhd'
		)
	);
?>