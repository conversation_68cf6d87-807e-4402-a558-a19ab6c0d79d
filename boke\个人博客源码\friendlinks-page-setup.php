<?php
/**
 * 友链页面自动创建脚本
 * 运行此脚本将自动创建友链页面
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    // 如果不在WordPress环境中，需要加载WordPress
    require_once(dirname(__FILE__) . '/wp-config.php');
}

function create_friendlinks_page() {
    // 检查是否已存在友链页面
    $existing_page = get_page_by_path('friendlinks');
    
    if ($existing_page) {
        echo "友链页面已存在，页面ID: " . $existing_page->ID . "\n";
        return $existing_page->ID;
    }
    
    // 创建友链页面
    $page_data = array(
        'post_title'    => '友情链接',
        'post_content'  => '
<div class="friendlinks-intro">
    <p>欢迎来到友情链接页面！这里展示了与本站互相链接的优秀网站。</p>
    <p>如果您希望与本站交换友链，请联系站长。</p>
</div>

[friendlinks]

<div class="friendlinks-apply">
    <h3>申请友链</h3>
    <p>如果您想申请友链，请确保您的网站符合以下要求：</p>
    <ul>
        <li>网站内容健康，无违法违规内容</li>
        <li>网站能够正常访问</li>
        <li>网站有一定的更新频率</li>
        <li>优先考虑技术、生活、学习类博客</li>
    </ul>
    <p>申请时请提供：网站名称、网站地址、网站描述、网站头像（可选）</p>
</div>
        ',
        'post_status'   => 'publish',
        'post_type'     => 'page',
        'post_name'     => 'friendlinks',
        'page_template' => 'page-friendlinks.php'
    );
    
    $page_id = wp_insert_post($page_data);
    
    if ($page_id) {
        // 设置页面模板
        update_post_meta($page_id, '_wp_page_template', 'page-friendlinks.php');
        echo "友链页面创建成功，页面ID: " . $page_id . "\n";
        echo "页面链接: " . get_permalink($page_id) . "\n";
        return $page_id;
    } else {
        echo "友链页面创建失败\n";
        return false;
    }
}

// 如果是通过命令行或直接访问运行
if (php_sapi_name() === 'cli' || (isset($_GET['action']) && $_GET['action'] === 'create_friendlinks_page')) {
    create_friendlinks_page();
}

// 也可以通过WordPress后台运行
add_action('admin_init', function() {
    if (isset($_GET['create_friendlinks_page']) && current_user_can('manage_options')) {
        create_friendlinks_page();
        wp_redirect(admin_url('edit.php?post_type=page'));
        exit;
    }
});

// 在插件激活时自动创建页面
register_activation_hook(__FILE__, 'create_friendlinks_page');
?>
