# 友链管理器

一个简单易用的WordPress友链管理插件，支持卡片式展示效果。

## 功能特点

- ✅ 简洁的后台管理界面
- ✅ 卡片式友链展示效果
- ✅ 支持头像显示
- ✅ 友链状态管理（启用/禁用）
- ✅ 自定义排序功能
- ✅ 响应式设计，适配移动端
- ✅ 短代码支持
- ✅ 主题模板支持

## 安装方法

1. 将插件文件夹上传到 `wp-content/plugins/` 目录
2. 在WordPress后台激活插件
3. 在左侧菜单中找到"友链管理"开始使用

## 使用方法

### 后台管理

1. **添加友链**：点击"友链管理" → "添加友链"
2. **管理友链**：在"所有友链"页面可以编辑、删除、启用/禁用友链
3. **排序**：通过设置排序数字来调整友链显示顺序

### 前端显示

#### 方法一：使用短代码
在页面或文章中插入短代码：
```
[friendlinks]
```

#### 方法二：在主题文件中调用
```php
<?php echo do_shortcode('[friendlinks]'); ?>
```

#### 方法三：使用专门的友链页面
1. 创建新页面
2. 选择"友链页面"模板
3. 发布页面

### 短代码参数

```
[friendlinks limit="6" status="1"]
```

- `limit`: 显示数量，-1为显示全部（默认：-1）
- `status`: 状态筛选，1为启用，0为禁用（默认：1）

## 数据库结构

插件会创建一个名为 `wp_friendlinks` 的数据表，包含以下字段：

- `id`: 主键ID
- `name`: 友链名称
- `url`: 友链地址
- `description`: 友链描述
- `avatar`: 头像地址
- `status`: 状态（1启用，0禁用）
- `sort_order`: 排序序号
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 样式自定义

如果需要自定义友链样式，可以在主题的 `style.css` 中覆盖以下CSS类：

```css
.friendlinks-container { /* 友链容器 */ }
.friendlink-card { /* 单个友链卡片 */ }
.friendlink-avatar { /* 头像容器 */ }
.friendlink-content { /* 内容区域 */ }
.friendlink-name { /* 友链名称 */ }
.friendlink-description { /* 友链描述 */ }
```

## 兼容性

- WordPress 5.0+
- PHP 7.4+
- 支持所有主流主题
- 响应式设计，支持移动端

## 更新日志

### 1.0.0
- 初始版本发布
- 基础友链管理功能
- 卡片式展示效果
- 响应式设计

## 技术支持

如有问题或建议，请联系开发者。

## 许可证

GPL v2 or later
