{"argon_theme_color": "#5e72e4", "argon_theme_color_hex_preview": "#5e72e4", "argon_show_customize_theme_color_picker": true, "argon_enable_immersion_color": "true", "argon_darkmode_autoswitch": "time", "argon_enable_amoled_dark": "false", "argon_card_radius": "15", "argon_card_shadow": "default", "argon_page_layout": "double", "argon_article_list_waterflow": "1", "argon_article_list_layout": "1", "argon_font": "serif", "argon_assets_path": "jsdelivr_fastly", "argon_custom_assets_path": "", "argon_wp_path": "/", "argon_dateformat": "YMD", "argon_enable_headroom": "true", "argon_toolbar_title": "法王", "argon_toolbar_icon": "", "argon_toolbar_icon_link": " ", "argon_toolbar_blur": "true", "argon_banner_title": "法王‘s blog", "argon_banner_subtitle": "Life is a coding,I will debug it.", "argon_banner_size": "fullscreen", "argon_page_background_banner_style": "transparent", "argon_show_toolbar_mask": true, "argon_banner_background_url": "--bing--", "argon_banner_background_color_type": "shape-primary", "argon_banner_background_hide_shapes": true, "argon_enable_banner_title_typing_effect": "true", "argon_banner_typing_effect_interval": "100", "argon_page_background_url": "http://**************:8080/wp-content/uploads/2025/01/IMG_20241229_144446.jpg", "argon_page_background_dark_url": "http://**************:8080/wp-content/uploads/2025/01/IMG_20241229_224323.jpg", "argon_page_background_opacity": "0.7", "argon_sidebar_banner_title": "法王公告栏", "argon_sidebar_banner_subtitle": "孩儿立志出乡关，学不成名誓不还", "argon_sidebar_auther_name": "法王", "argon_sidebar_auther_image": "http://**************:8080/wp-content/uploads/2025/01/可爱头像.jpg", "argon_sidebar_author_description": "痴迷于安全技术的小白帽", "argon_sidebar_announcement": "记录自己技术增长过程的博客~", "argon_fab_show_settings_button": "false", "argon_fab_show_darkmode_button": "true", "argon_fab_show_gotocomment_button": "false", "argon_seo_description": "网站描述 (Description Meta 标签)", "argon_seo_keywords": "搜索引擎关键词（Keywords Meta 标签）", "argon_article_meta": "time|categories|views", "argon_show_readingtime": "true", "argon_reading_speed": "580", "argon_reading_speed_en": "80", "argon_reading_speed_code": "10", "argon_show_thumbnail_in_banner_in_content_page": "false", "argon_first_image_as_thumbnail_by_default": "true", "argon_reference_list_title": "参考", "argon_show_sharebtn": "true", "argon_show_headindex_number": "false", "argon_donate_qrcode_url": "", "argon_additional_content_after_post": "文末附加内容", "argon_related_post": "category,tag", "argon_related_post_sort_orderby": "meta_value_num", "argon_related_post_sort_order": "DESC", "argon_related_post_limit": "10", "argon_article_header_style": "article-header-style-2", "argon_outdated_info_time_type": "createdtime", "argon_outdated_info_days": "1", "argon_outdated_info_tip_type": "inpost", "argon_outdated_info_tip_content": "本文最后更新于%modify_date_delta% 天前，其中的信息可能已经过时，如有错误请发送邮件到******************", "argon_archives_timeline_show_month": "true", "argon_archives_timeline_url": "", "argon_footer_html": "<style>\n/* 核心样式 */\n.github-badge {\ndisplay: inline-block;\nborder-radius: 4px;\ntext-shadow: none;\nfont-size: 13.1px;\ncolor: #fff;\nline-height: 15px;\nmargin-bottom: 5px;\nfont-family: \"Open Sans\", sans-serif;\n}\n.github-badge .badge-subject {\ndisplay: inline-block;\nbackground-color: #4d4d4d;\npadding: 4px 4px 4px 6px;\nborder-top-left-radius: 4px;\nborder-bottom-left-radius: 4px;\nfont-family: \"Open Sans\", sans-serif;\n}\n.github-badge .badge-value {\ndisplay: inline-block;\npadding: 4px 6px 4px 4px;\nborder-top-right-radius: 4px;\nborder-bottom-right-radius: 4px;\nfont-family: \"Open Sans\", sans-serif;\n}\n.github-badge-big {\ndisplay: inline-block;\nborder-radius: 6px;\ntext-shadow: none;\nfont-size: 14.1px;\ncolor: #fff;\nline-height: 18px;\nmargin-bottom: 7px;\n}\n.github-badge-big .badge-subject {\ndisplay: inline-block;\nbackground-color: #4d4d4d;\npadding: 4px 4px 4px 6px;\nborder-top-left-radius: 4px;\nborder-bottom-left-radius: 4px;\n}\n.github-badge-big .badge-value {\ndisplay: inline-block;\npadding: 4px 6px 4px 4px;\nborder-top-right-radius: 4px;\nborder-bottom-right-radius: 4px;\n}\n.bg-orange {\nbackground-color: #ec8a64 !important;\n}\n.bg-red {\nbackground-color: #cb7574 !important;\n}\n.bg-apricots {\nbackground-color: #f7c280 !important;\n}\n.bg-casein {\nbackground-color: #dfe291 !important;\n}\n.bg-shallots {\nbackground-color: #97c3c6 !important;\n}\n.bg-ogling {\nbackground-color: #95c7e0 !important;\n}\n.bg-haze {\nbackground-color: #9aaec7 !important;\n}\n.bg-mountain-terrier {\nbackground-color: #99a5cd !important;\n}\n</style>\n \n<div class=\"github-badge-big\">\n<span class=\"badge-subject\"><i class=\"fa fa-id-card\"></i> 备案号 </span\n><span class=\"badge-value bg-orange\">\n<a href=\"https://beian.miit.gov.cn/\" target=\"_blank\" one-link-mark=\"yes\"\n>苏ICP备0000000000号</a\n>\n|\n<a\nhref=\"http://www.beian.gov.cn/portal/registerSystemInfo?recordcode= 32072202010255\"\ntarget=\"_blank\"\none-link-mark=\"yes\"\n>苏公网安备 00000000000000号</a\n></span\n>\n</div>\n<div class=\"github-badge-big\">\n        <span class=\"badge-subject\"><i class=\"fa fa-cloud\" aria-hidden=\"true\"></i> CDN</span>\n        <span class=\"badge-value bg-shallots\">\n            <a href=\"https://www.upyun.com/\" target=\"_blank\" one-link-mark=\"yes\">Upyun</a>\n        </span>\n<span class=\"badge-subject\"><i class=\"fa fa-wordpress\"></i> Powered</span\n><span class=\"badge-value bg-green\"\n><a href=\"https://cn.wordpress.org/\" target=\"_blank\" one-link-mark=\"yes\"\n>WordPress</a\n></span\n>\n</div>\n<div class=\"github-badge-big\">\n<span class=\"badge-subject\">Copyright </span\n><span class=\"badge-value bg-red\">\n2022-2022\n<i class=\"fa fa-copyright\"></i> Fawang</span\n>\n</script>\n</div>\n<div class=\"github-badge-big\">\n<span class=\"badge-subject\"><i class=\"fa fa-clock-o\"></i> Running Time</span\n><span class=\"badge-value bg-apricots\"\n><span id=\"blog_running_days\" class=\"odometer odometer-auto-theme\"></span>\ndays\n<span id=\"blog_running_hours\" class=\"odometer odometer-auto-theme\"></span> H\n<span id=\"blog_running_mins\" class=\"odometer odometer-auto-theme\"></span> M\n<span id=\"blog_running_secs\" class=\"odometer odometer-auto-theme\"></span>\nS</span\n>\n <script no-pjax=\"\">\nvar blog_running_days = document.getElementById(\"blog_running_days\");\nvar blog_running_hours = document.getElementById(\"blog_running_hours\");\nvar blog_running_mins = document.getElementById(\"blog_running_mins\");\nvar blog_running_secs = document.getElementById(\"blog_running_secs\");\nfunction refresh_blog_running_time() {\nvar time = new Date() - new Date(2020, 0,0, 0, 0, 0);\nvar d = parseInt(time / 24 / 60 / 60 / 1000);\nvar h = parseInt((time % (24 * 60 * 60 * 1000)) / 60 / 60 / 1000);\nvar m = parseInt((time % (60 * 60 * 1000)) / 60 / 1000);\nvar s = parseInt((time % (60 * 1000)) / 1000);\nblog_running_days.innerHTML = d;\nblog_running_hours.innerHTML = h;\nblog_running_mins.innerHTML = m;\nblog_running_secs.innerHTML = s;\n}\nrefresh_blog_running_time();\nif (typeof bottomTimeIntervalHasSet == \"undefined\") {\nvar bottomTimeIntervalHasSet = true;\nsetInterval(function () {\nrefresh_blog_running_time();\n}, 500);\n}\n</script>\n<!--live2d--> \n<script src=\"/wp-content/themes/argon/argon/live2d/TweenLite.js\"></script> \n<script src=\"/wp-content/themes/argon/argon/live2d/live2dcubismcore.min.js\"></script>\n<script src=\"/wp-content/themes/argon/argon/live2d/pixi.min.js\"></script> \n<script src=\"/wp-content/themes/argon/argon/live2d/cubism4.min.js\"></script> \n<link href=\"/wp-content/themes/argon/argon/live2d/pio.css\" rel=\"stylesheet\" type=\"text/css\"/> \n<script src=\"/wp-content/themes/argon/argon/live2d/pio.js\"></script> \n<script src=\"/wp-content/themes/argon/argon/live2d/pio_sdk4.js\"></script> \n<script src=\"/wp-content/themes/argon/argon/live2d/load.js\"></script>", "argon_enable_code_highlight": "true", "argon_code_theme": "vs2015", "argon_code_highlight_hide_linenumber": "true", "argon_code_highlight_break_line": "false", "argon_code_highlight_transparent_linenumber": "false", "argon_math_render": "none", "argon_mathjax_cdn_url": "//cdn.jsdelivr.net/npm/mathjax@3/es5/tex-chtml-full.js", "argon_mathjax_v2_cdn_url": "//cdn.jsdelivr.net/npm/mathjax@2.7.5/MathJax.js?config=TeX-AMS_HTML", "argon_katex_cdn_url": "//cdn.jsdelivr.net/npm/katex@0.11.1/dist/", "argon_enable_lazyload": "true", "argon_lazyload_threshold": "800", "argon_lazyload_effect": "fadeIn", "argon_lazyload_loading_style": "1", "argon_enable_fancybox": "true", "argon_enable_zoomify": "false", "argon_zoomify_duration": "200", "argon_zoomify_easing": "cubic-bezier(0.4,0,0,1)", "argon_zoomify_scale": "0.9", "argon_enable_pangu": "article", "argon_custom_html_head": "", "argon_custom_html_foot": "", "argon_enable_smoothscroll_type": "1_pulse", "argon_enable_into_article_animation": "true", "argon_disable_pjax_animation": "false", "argon_comment_pagination_type": "page", "argon_comment_emotion_keyboard": "true", "argon_hide_name_email_site_input": "false", "argon_comment_need_captcha": "false", "argon_get_captcha_by_ajax": "false", "argon_comment_allow_markdown": "true", "argon_comment_allow_editing": "true", "argon_comment_allow_privatemode": "true", "argon_comment_allow_mailnotice": "true", "argon_comment_mailnotice_checkbox_checked": true, "argon_comment_enable_qq_avatar": "true", "argon_comment_avatar_vcenter": "false", "argon_who_can_visit_comment_edit_history": "commentsender", "argon_enable_comment_pinning": "true", "argon_enable_comment_upvote": "true", "argon_comment_ua": "platform,browser", "argon_show_comment_parent_info": "true", "argon_fold_long_comments": "true", "argon_gravatar_cdn": "", "argon_text_gravatar": "true", "argon_enable_search_filters": "true", "argon_search_filters_type": "*post,*page,shuoshuo", "argon_pjax_disabled": "false", "argon_hide_categories": "", "argon_enable_login_css": "true", "argon_home_show_shuoshuo": "false", "argon_fold_long_shuoshuo": "true", "argon_enable_timezone_fix": "false", "argon_hide_shortcode_in_preview": "true", "argon_trim_words_count": "0", "argon_enable_mobile_scale": "false", "argon_disable_googlefont": "false", "argon_disable_codeblock_style": "false", "argon_update_source": "github", "argon_hide_footer_author": "true"}