<?php
/**
 * Template Name: 友链页面
 * 
 * 专门用于显示友链的页面模板
 */

get_header(); ?>

<div class="container">
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <?php while (have_posts()) : the_post(); ?>
                <article class="post-preview">
                    <header class="post-header">
                        <h1 class="post-title"><?php the_title(); ?></h1>
                        <?php if (get_the_content()): ?>
                        <div class="post-content">
                            <?php the_content(); ?>
                        </div>
                        <?php endif; ?>
                    </header>
                    
                    <div class="friendlinks-section">
                        <?php
                        // 显示友链
                        echo do_shortcode('[friendlinks]');
                        ?>
                    </div>
                    
                    <?php if (is_user_logged_in() && current_user_can('manage_options')): ?>
                    <div class="admin-links" style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h4>管理员操作</h4>
                        <p>
                            <a href="<?php echo admin_url('admin.php?page=friendlinks-manager'); ?>" class="btn btn-primary btn-sm">
                                管理友链
                            </a>
                            <a href="<?php echo admin_url('admin.php?page=friendlinks-add'); ?>" class="btn btn-success btn-sm">
                                添加友链
                            </a>
                        </p>
                    </div>
                    <?php endif; ?>
                </article>
            <?php endwhile; ?>
        </div>
    </div>
</div>

<style>
/* 页面特定样式 */
.friendlinks-section {
    margin: 30px 0;
}

.post-header {
    text-align: center;
    margin-bottom: 40px;
}

.post-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e1e1e;
    margin-bottom: 20px;
}

.post-content {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #666;
    margin-bottom: 20px;
}

.admin-links {
    border-left: 4px solid #007cba;
}

.admin-links h4 {
    margin-top: 0;
    color: #007cba;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    margin-right: 10px;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007cba;
    color: white;
}

.btn-primary:hover {
    background-color: #005a87;
    color: white;
}

.btn-success {
    background-color: #46b450;
    color: white;
}

.btn-success:hover {
    background-color: #368a3c;
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .post-title {
        font-size: 2rem;
    }
    
    .post-content {
        font-size: 1rem;
    }
    
    .admin-links {
        margin-left: -15px;
        margin-right: -15px;
        border-radius: 0;
    }
}
</style>

<?php get_footer(); ?>
