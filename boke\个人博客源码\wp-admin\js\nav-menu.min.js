/*! This file is auto-generated */
!function(k){var I=window.wpNavMenu={options:{menuItemDepthPerLevel:30,globalMaxDepth:11,sortableItems:"> *",targetTolerance:0},menuList:void 0,targetList:void 0,menusChanged:!1,isRTL:!("undefined"==typeof isRtl||!isRtl),negateIfRTL:"undefined"!=typeof isRtl&&isRtl?-1:1,lastSearch:"",init:function(){I.menuList=k("#menu-to-edit"),I.targetList=I.menuList,this.jQueryExtensions(),this.attachMenuEditListeners(),this.attachBulkSelectButtonListeners(),this.attachMenuCheckBoxListeners(),this.attachMenuItemDeleteButton(),this.attachPendingMenuItemsListForDeletion(),this.attachQuickSearchListeners(),this.attachThemeLocationsListeners(),this.attachMenuSaveSubmitListeners(),this.attachTabsPanelListeners(),this.attachUnsavedChangesListener(),I.menuList.length&&this.initSortables(),menus.oneThemeLocationNoMenus&&k("#posttype-page").addSelectedToMenu(I.addMenuItemToBottom),this.initManageLocations(),this.initAccessibility(),this.initToggles(),this.initPreviewing()},jQueryExtensions:function(){k.fn.extend({menuItemDepth:function(){var e=I.isRTL?this.eq(0).css("margin-right"):this.eq(0).css("margin-left");return I.pxToDepth(e&&-1!=e.indexOf("px")?e.slice(0,-2):0)},updateDepthClass:function(t,n){return this.each(function(){var e=k(this);n=n||e.menuItemDepth(),k(this).removeClass("menu-item-depth-"+n).addClass("menu-item-depth-"+t)})},shiftDepthClass:function(i){return this.each(function(){var e=k(this),t=e.menuItemDepth(),n=t+i;e.removeClass("menu-item-depth-"+t).addClass("menu-item-depth-"+n),0===n&&e.find(".is-submenu").hide()})},childMenuItems:function(){var i=k();return this.each(function(){for(var e=k(this),t=e.menuItemDepth(),n=e.next(".menu-item");n.length&&n.menuItemDepth()>t;)i=i.add(n),n=n.next(".menu-item")}),i},shiftHorizontally:function(n){return this.each(function(){var e=k(this),t=e.menuItemDepth();e.moveHorizontally(t+n,t)})},moveHorizontally:function(a,s){return this.each(function(){var e=k(this),t=e.childMenuItems(),n=a-s,i=e.find(".is-submenu");e.updateDepthClass(a,s).updateParentMenuItemDBId(),t&&t.each(function(){var e=k(this),t=e.menuItemDepth();e.updateDepthClass(t+n,t).updateParentMenuItemDBId()}),0===a?i.hide():i.show()})},updateParentMenuItemDBId:function(){return this.each(function(){var e=k(this),t=e.find(".menu-item-data-parent-id"),n=parseInt(e.menuItemDepth(),10),e=e.prevAll(".menu-item-depth-"+(n-1)).first();0===n?t.val(0):t.val(e.find(".menu-item-data-db-id").val())})},hideAdvancedMenuItemFields:function(){return this.each(function(){var e=k(this);k(".hide-column-tog").not(":checked").each(function(){e.find(".field-"+k(this).val()).addClass("hidden-field")})})},addSelectedToMenu:function(a){return 0!==k("#menu-to-edit").length&&this.each(function(){var e=k(this),n={},t=menus.oneThemeLocationNoMenus&&0===e.find(".tabs-panel-active .categorychecklist li input:checked").length?e.find('#page-all li input[type="checkbox"]'):e.find(".tabs-panel-active .categorychecklist li input:checked"),i=/menu-item\[([^\]]*)/;if(a=a||I.addMenuItemToBottom,!t.length)return!1;e.find(".button-controls .spinner").addClass("is-active"),k(t).each(function(){var e=k(this),t=i.exec(e.attr("name")),t=void 0===t[1]?0:parseInt(t[1],10);this.className&&-1!=this.className.indexOf("add-to-top")&&(a=I.addMenuItemToTop),n[t]=e.closest("li").getItemData("add-menu-item",t)}),I.addItemToMenu(n,a,function(){t.prop("checked",!1),e.find(".button-controls .select-all").prop("checked",!1),e.find(".button-controls .spinner").removeClass("is-active"),e.updateParentDropdown(),e.updateOrderDropdown()})})},getItemData:function(t,n){t=t||"menu-item";var i,a={},s=["menu-item-db-id","menu-item-object-id","menu-item-object","menu-item-parent-id","menu-item-position","menu-item-type","menu-item-title","menu-item-url","menu-item-description","menu-item-attr-title","menu-item-target","menu-item-classes","menu-item-xfn"];return(n=n||"menu-item"!=t?n:this.find(".menu-item-data-db-id").val())&&this.find("input").each(function(){var e;for(i=s.length;i--;)"menu-item"==t?e=s[i]+"["+n+"]":"add-menu-item"==t&&(e="menu-item["+n+"]["+s[i]+"]"),this.name&&e==this.name&&(a[s[i]]=this.value)}),a},setItemData:function(e,a,s){return a=a||"menu-item",(s=s||"menu-item"!=a?s:k(".menu-item-data-db-id",this).val())&&this.find("input").each(function(){var n,i=k(this);k.each(e,function(e,t){"menu-item"==a?n=e+"["+s+"]":"add-menu-item"==a&&(n="menu-item["+s+"]["+e+"]"),n==i.attr("name")&&i.val(t)})}),this},updateParentDropdown:function(){return this.each(function(){var o=k("#menu-to-edit li"),e=k(".edit-menu-item-parent");k.each(e,function(){var e=k(this),i="",t="",n=e.closest("li.menu-item").find(".menu-item-data-db-id").val(),a=e.closest("li.menu-item").find(".menu-item-data-parent-id").val(),s=e.closest("li.menu-item").childMenuItems(),m=[n];0<s.length&&k.each(s,function(){var e=k(this).find(".menu-item-data-db-id").val();m.push(e)}),i+="<option "+(t=0==a?"selected":t)+' value="0">'+wp.i18n._x("No Parent","menu item without a parent in navigation menu")+"</option>",k.each(o,function(){var e=k(this),t="",n=e.find(".menu-item-data-db-id").val(),e=e.find(".edit-menu-item-title").val();m.includes(n)||(i+="<option "+(t=a==n?"selected":t)+' value="'+n+'">'+e+"</option>")}),e.html(i)})})},updateOrderDropdown:function(){return this.each(function(){var l,e=k(".edit-menu-item-order");k.each(e,function(){var e=k(this),t=e.closest("li.menu-item").first(),n=t.menuItemDepth(),i="",a="";if(0===n){var s=k(".menu-item-depth-0"),m=s.length;l=s.index(t)+1;for(let e=1;e<m+1;e++){a="",e==l&&(a="selected");var o=wp.i18n.sprintf(wp.i18n._x("%1$s of %2$s","part of a total number of menu items"),e,m);i+="<option "+a+' value="'+e+'">'+o+"</option>"}}else{var s=t.prevAll(".menu-item-depth-"+parseInt(n-1,10)).first().find(".menu-item-data-db-id").val(),n=k('.menu-item .menu-item-data-parent-id[value="'+s+'"]'),u=n.length;l=k(n.parents(".menu-item").get().reverse()).index(t)+1;for(let e=1;e<u+1;e++){a="",e==l&&(a="selected");var d=wp.i18n.sprintf(wp.i18n._x("%1$s of %2$s","part of a total number of menu items"),e,u);i+="<option "+a+' value="'+e+'">'+d+"</option>"}}e.html(i)})})}})},countMenuItems:function(e){return k(".menu-item-depth-"+e).length},moveMenuItem:function(e,t){var n,i,a=k("#menu-to-edit li"),s=a.length,m=e.parents("li.menu-item"),o=m.childMenuItems(),u=m.getItemData(),d=parseInt(m.menuItemDepth(),10),l=parseInt(m.index(),10),r=m.next(),c=r.childMenuItems(),h=parseInt(r.menuItemDepth(),10)+1,p=m.prev(),f=parseInt(p.menuItemDepth(),10),v=p.getItemData()["menu-item-db-id"],p=menus["moved"+t.charAt(0).toUpperCase()+t.slice(1)];switch(t){case"up":i=l-1,0!==l&&(0==i&&0!==d&&m.moveHorizontally(0,d),0!==f&&m.moveHorizontally(f,d),(o?n=m.add(o):m).detach().insertBefore(a.eq(i)).updateParentMenuItemDBId());break;case"down":if(o){if(n=m.add(o),(c=0!==(r=a.eq(n.length+l)).childMenuItems().length)&&(i=parseInt(r.menuItemDepth(),10)+1,m.moveHorizontally(i,d)),s===l+n.length)break;n.detach().insertAfter(a.eq(l+n.length)).updateParentMenuItemDBId()}else{if(0!==c.length&&m.moveHorizontally(h,d),s===l+1)break;m.detach().insertAfter(a.eq(l+1)).updateParentMenuItemDBId()}break;case"top":0!==l&&(o?n=m.add(o):m).detach().insertBefore(a.eq(0)).updateParentMenuItemDBId();break;case"left":0!==d&&m.shiftHorizontally(-1);break;case"right":0!==l&&u["menu-item-parent-id"]!==v&&m.shiftHorizontally(1)}e.trigger("focus"),I.registerChange(),I.refreshKeyboardAccessibility(),I.refreshAdvancedAccessibility(),m.updateParentDropdown(),m.updateOrderDropdown(),p&&wp.a11y.speak(p)},initAccessibility:function(){var e=k("#menu-to-edit");I.refreshKeyboardAccessibility(),I.refreshAdvancedAccessibility(),e.on("mouseenter.refreshAccessibility focus.refreshAccessibility touchstart.refreshAccessibility",".menu-item",function(){I.refreshAdvancedAccessibilityOfItem(k(this).find("a.item-edit"))}),e.on("click","a.item-edit",function(){I.refreshAdvancedAccessibilityOfItem(k(this))}),e.on("click",".menus-move",function(){var e=k(this).data("dir");void 0!==e&&I.moveMenuItem(k(this).parents("li.menu-item").find("a.item-edit"),e)}),e.updateParentDropdown(),e.updateOrderDropdown(),e.on("change",".edit-menu-item-parent",function(){I.changeMenuParent(k(this))}),e.on("change",".edit-menu-item-order",function(){I.changeMenuOrder(k(this))})},changeMenuParent:function(e){var t=k("#menu-to-edit li"),e=k(e),n=e.val(),i=e.closest("li.menu-item").first(),a=i.menuItemDepth(),s=i.childMenuItems(),m=parseInt(i.childMenuItems().length,10),o=k("#menu-item-"+n),u=o.menuItemDepth(),u=parseInt(u)+1,u=(0==n&&(u=0),i.find(".menu-item-data-parent-id").val(n),i.moveHorizontally(u,a),(i=0<m?i.add(s):i).detach(),t=k("#menu-to-edit li"),parseInt(o.index(),10)),a=parseInt(o.childMenuItems().length,10),m=0<a?u+a:u;0==n&&(m=t.length-1),i.insertAfter(t.eq(m)).updateParentMenuItemDBId().updateParentDropdown().updateOrderDropdown(),I.registerChange(),I.refreshKeyboardAccessibility(),I.refreshAdvancedAccessibility(),e.trigger("focus"),wp.a11y.speak(menus.parentUpdated,"polite")},changeMenuOrder:function(e){var t=k("#menu-to-edit li"),e=k(e),n=parseInt(e.val(),10),i=e.closest("li.menu-item").first(),a=i.childMenuItems(),s=a.length,m=parseInt(i.index(),10),o=i.find(".menu-item-data-parent-id").val(),o=k('.menu-item .menu-item-data-parent-id[value="'+o+'"]'),o=k(o[n-1]).closest("li.menu-item"),n=(0<s&&(i=i.add(a)),o.childMenuItems().length),s=parseInt(o.index(),10),t=k("#menu-to-edit li"),a=s;(a<m?(a=s,i.detach().insertBefore(t.eq(a))):(a+=n,i.detach().insertAfter(t.eq(a)))).updateOrderDropdown(),I.registerChange(),I.refreshKeyboardAccessibility(),I.refreshAdvancedAccessibility(),e.trigger("focus"),wp.a11y.speak(menus.orderUpdated,"polite")},refreshAdvancedAccessibilityOfItem:function(e){var t,n,i,a,s,m,o,u,d,l,r,c,h;!0===k(e).data("needs_accessibility_refresh")&&(m=0===(s=(a=(e=k(e)).closest("li.menu-item").first()).menuItemDepth()),o=e.closest(".menu-item-handle").find(".menu-item-title").text(),u=e.closest(".menu-item-handle").find(".item-controls").find(".item-type").text(),d=parseInt(a.index(),10),l=m?s:parseInt(s-1,10),l=a.prevAll(".menu-item-depth-"+l).first().find(".menu-item-title").text(),r=a.prevAll(".menu-item-depth-"+s).first().find(".menu-item-title").text(),c=k("#menu-to-edit li").length,h=a.nextAll(".menu-item-depth-"+s).length,a.find(".field-move").toggle(1<c),0!==d&&(n=a.find(".menus-move-up")).attr("aria-label",menus.moveUp).css("display","inline"),0!==d&&m&&(n=a.find(".menus-move-top")).attr("aria-label",menus.moveToTop).css("display","inline"),d+1!==c&&0!==d&&(n=a.find(".menus-move-down")).attr("aria-label",menus.moveDown).css("display","inline"),0===d&&0!==h&&(n=a.find(".menus-move-down")).attr("aria-label",menus.moveDown).css("display","inline"),m||(n=a.find(".menus-move-left"),i=menus.outFrom.replace("%s",l),n.attr("aria-label",menus.moveOutFrom.replace("%s",l)).text(i).css("display","inline")),0!==d&&a.find(".menu-item-data-parent-id").val()!==a.prev().find(".menu-item-data-db-id").val()&&(n=a.find(".menus-move-right"),i=menus.under.replace("%s",r),n.attr("aria-label",menus.moveUnder.replace("%s",r)).text(i).css("display","inline")),m=m?(t=(h=k(".menu-item-depth-0")).index(a)+1,c=h.length,menus.menuFocus.replace("%1$s",o).replace("%2$s",u).replace("%3$d",t).replace("%4$d",c)):(d=(l=a.prevAll(".menu-item-depth-"+parseInt(s-1,10)).first()).find(".menu-item-data-db-id").val(),n=l.find(".menu-item-title").text(),i=(r=k('.menu-item .menu-item-data-parent-id[value="'+d+'"]')).length,t=k(r.parents(".menu-item").get().reverse()).index(a)+1,s<2?menus.subMenuFocus.replace("%1$s",o).replace("%2$s",u).replace("%3$d",t).replace("%4$d",i).replace("%5$s",n):menus.subMenuMoreDepthFocus.replace("%1$s",o).replace("%2$s",u).replace("%3$d",t).replace("%4$d",i).replace("%5$s",n).replace("%6$d",s)),e.attr("aria-label",m),e.data("needs_accessibility_refresh",!1))},refreshAdvancedAccessibility:function(){k(".menu-item-settings .field-move .menus-move").hide(),k("a.item-edit").data("needs_accessibility_refresh",!0),k(".menu-item-edit-active a.item-edit").each(function(){I.refreshAdvancedAccessibilityOfItem(this)})},refreshKeyboardAccessibility:function(){k("a.item-edit").off("focus").on("focus",function(){k(this).off("keydown").on("keydown",function(e){var t,n=k(this),i=n.parents("li.menu-item").getItemData();if((37==e.which||38==e.which||39==e.which||40==e.which)&&(n.off("keydown"),1!==k("#menu-to-edit li").length)){switch(t={38:"up",40:"down",37:"left",39:"right"},(t=k("body").hasClass("rtl")?{38:"up",40:"down",39:"left",37:"right"}:t)[e.which]){case"up":I.moveMenuItem(n,"up");break;case"down":I.moveMenuItem(n,"down");break;case"left":I.moveMenuItem(n,"left");break;case"right":I.moveMenuItem(n,"right")}return k("#edit-"+i["menu-item-db-id"]).trigger("focus"),!1}})})},initPreviewing:function(){k("#menu-to-edit").on("change input",".edit-menu-item-title",function(e){var e=k(e.currentTarget),t=e.val(),e=e.closest(".menu-item").find(".menu-item-title");t?e.text(t).removeClass("no-title"):e.text(wp.i18n._x("(no label)","missing menu item navigation label")).addClass("no-title")})},initToggles:function(){postboxes.add_postbox_toggles("nav-menus"),columns.useCheckboxesForHidden(),columns.checked=function(e){k(".field-"+e).removeClass("hidden-field")},columns.unchecked=function(e){k(".field-"+e).addClass("hidden-field")},I.menuList.hideAdvancedMenuItemFields(),k(".hide-postbox-tog").on("click",function(){var e=k(".accordion-container li.accordion-section").filter(":hidden").map(function(){return this.id}).get().join(",");k.post(ajaxurl,{action:"closed-postboxes",hidden:e,closedpostboxesnonce:jQuery("#closedpostboxesnonce").val(),page:"nav-menus"})})},initSortables:function(){var m,a,s,n,o,u,d,l,r,c,e,h=0,p=I.menuList.offset().left,f=k("body"),v=f[0].className&&(e=f[0].className.match(/menu-max-depth-(\d+)/))&&e[1]?parseInt(e[1],10):0;function g(e){n=e.placeholder.prev(".menu-item"),o=e.placeholder.next(".menu-item"),n[0]==e.item[0]&&(n=n.prev(".menu-item")),o[0]==e.item[0]&&(o=o.next(".menu-item")),u=n.length?n.offset().top+n.height():0,d=o.length?o.offset().top+o.height()/3:0,a=o.length?o.menuItemDepth():0,s=n.length?(e=n.menuItemDepth()+1)>I.options.globalMaxDepth?I.options.globalMaxDepth:e:0}function b(e,t){e.placeholder.updateDepthClass(t,h),h=t}0!==k("#menu-to-edit li").length&&k(".drag-instructions").show(),p+=I.isRTL?I.menuList.width():0,I.menuList.sortable({handle:".menu-item-handle",placeholder:"sortable-placeholder",items:I.options.sortableItems,start:function(e,t){var n,i;I.isRTL&&(t.item[0].style.right="auto"),r=t.item.children(".menu-item-transport"),m=t.item.menuItemDepth(),b(t,m),i=(t.item.next()[0]==t.placeholder[0]?t.item.next():t.item).childMenuItems(),r.append(i),n=r.outerHeight(),n=(n+=0<n?+t.placeholder.css("margin-top").slice(0,-2):0)+t.helper.outerHeight(),l=n,t.placeholder.height(n-=2),c=m,i.each(function(){var e=k(this).menuItemDepth();c=c<e?e:c}),n=t.helper.find(".menu-item-handle").outerWidth(),n+=I.depthToPx(c-m),t.placeholder.width(n-=2),(i=t.placeholder.next(".menu-item")).css("margin-top",l+"px"),t.placeholder.detach(),k(this).sortable("refresh"),t.item.after(t.placeholder),i.css("margin-top",0),g(t)},stop:function(e,t){var n=h-m,i=r.children().insertAfter(t.item),a=t.item.find(".item-title .is-submenu");if(0<h?a.show():a.hide(),0!=n){t.item.updateDepthClass(h),i.shiftDepthClass(n);var a=n,s=v;if(0!==a){if(0<a)v<(i=c+a)&&(s=i);else if(a<0&&c==v)for(;!k(".menu-item-depth-"+s,I.menuList).length&&0<s;)s--;f.removeClass("menu-max-depth-"+v).addClass("menu-max-depth-"+s),v=s}}I.registerChange(),t.item.updateParentMenuItemDBId(),t.item[0].style.top=0,I.isRTL&&(t.item[0].style.left="auto",t.item[0].style.right=0),I.refreshKeyboardAccessibility(),I.refreshAdvancedAccessibility(),t.item.updateParentDropdown(),t.item.updateOrderDropdown(),I.refreshAdvancedAccessibilityOfItem(t.item.find("a.item-edit"))},change:function(e,t){t.placeholder.parent().hasClass("menu")||(n.length?n.after(t.placeholder):I.menuList.prepend(t.placeholder)),g(t)},sort:function(e,t){var n=t.helper.offset(),i=I.isRTL?n.left+t.helper.width():n.left,i=I.negateIfRTL*I.pxToDepth(i-p);s<i||n.top<u-I.options.targetTolerance?i=s:i<a&&(i=a),i!=h&&b(t,i),d&&n.top+l>d&&(o.after(t.placeholder),g(t),k(this).sortable("refreshPositions"))}})},initManageLocations:function(){k("#menu-locations-wrap form").on("submit",function(){window.onbeforeunload=null}),k(".menu-location-menus select").on("change",function(){var e=k(this).closest("tr").find(".locations-edit-menu-link");k(this).find("option:selected").data("orig")?e.show():e.hide()})},attachMenuEditListeners:function(){var t=this;k("#update-nav-menu").on("click",function(e){if(e.target&&e.target.className)return-1!=e.target.className.indexOf("item-edit")?t.eventOnClickEditLink(e.target):-1!=e.target.className.indexOf("menu-save")?t.eventOnClickMenuSave(e.target):-1!=e.target.className.indexOf("menu-delete")?t.eventOnClickMenuDelete(e.target):-1!=e.target.className.indexOf("item-delete")?t.eventOnClickMenuItemDelete(e.target):-1!=e.target.className.indexOf("item-cancel")?t.eventOnClickCancelLink(e.target):void 0}),k("#menu-name").on("input",_.debounce(function(){var e=k(document.getElementById("menu-name")),t=e.val();t&&t.replace(/\s+/,"")?e.parent().removeClass("form-invalid"):e.parent().addClass("form-invalid")},500)),k('#add-custom-links input[type="text"]').on("keypress",function(e){k("#customlinkdiv").removeClass("form-invalid"),13===e.keyCode&&(e.preventDefault(),k("#submit-customlinkdiv").trigger("click"))})},attachBulkSelectButtonListeners:function(){var e=this;k(".bulk-select-switcher").on("change",function(){this.checked?(k(".bulk-select-switcher").prop("checked",!0),e.enableBulkSelection()):(k(".bulk-select-switcher").prop("checked",!1),e.disableBulkSelection())})},enableBulkSelection:function(){var e=k("#menu-to-edit .menu-item-checkbox");k("#menu-to-edit").addClass("bulk-selection"),k("#nav-menu-bulk-actions-top").addClass("bulk-selection"),k("#nav-menu-bulk-actions-bottom").addClass("bulk-selection"),k.each(e,function(){k(this).prop("disabled",!1)})},disableBulkSelection:function(){var e=k("#menu-to-edit .menu-item-checkbox");k("#menu-to-edit").removeClass("bulk-selection"),k("#nav-menu-bulk-actions-top").removeClass("bulk-selection"),k("#nav-menu-bulk-actions-bottom").removeClass("bulk-selection"),k(".menu-items-delete").is('[aria-describedby="pending-menu-items-to-delete"]')&&k(".menu-items-delete").removeAttr("aria-describedby"),k.each(e,function(){k(this).prop("disabled",!0).prop("checked",!1)}),k(".menu-items-delete").addClass("disabled"),k("#pending-menu-items-to-delete ul").empty()},attachMenuCheckBoxListeners:function(){var e=this;k("#menu-to-edit").on("change",".menu-item-checkbox",function(){e.setRemoveSelectedButtonStatus()})},attachMenuItemDeleteButton:function(){var t=this;k(document).on("click",".menu-items-delete",function(e){var n,i;e.preventDefault(),k(this).hasClass("disabled")||(k.each(k(".menu-item-checkbox:checked"),function(e,t){k(t).parents("li").find("a.item-delete").trigger("click")}),k(".menu-items-delete").addClass("disabled"),k(".bulk-select-switcher").prop("checked",!1),n="",i=k("#pending-menu-items-to-delete ul li"),k.each(i,function(e,t){t=k(t).find(".pending-menu-item-name").text(),t=menus.menuItemDeletion.replace("%s",t);n+=t,e+1<i.length&&(n+=", ")}),e=menus.itemsDeleted.replace("%s",n),wp.a11y.speak(e,"polite"),t.disableBulkSelection(),menus.updateParentDropdown(),menus.updateOrderDropdown())})},attachPendingMenuItemsListForDeletion:function(){k("#post-body-content").on("change",".menu-item-checkbox",function(){var e,t,n,i;k(".menu-items-delete").is('[aria-describedby="pending-menu-items-to-delete"]')||k(".menu-items-delete").attr("aria-describedby","pending-menu-items-to-delete"),e=k(this).next().text(),t=k(this).parent().next(".item-controls").find(".item-type").text(),n=k(this).attr("data-menu-item-id"),0<(i=k("#pending-menu-items-to-delete ul").find("[data-menu-item-id="+n+"]")).length&&i.remove(),!0===this.checked&&k("#pending-menu-items-to-delete ul").append('<li data-menu-item-id="'+n+'"><span class="pending-menu-item-name">'+e+'</span> <span class="pending-menu-item-type">('+t+')</span><span class="separator"></span></li>'),k("#pending-menu-items-to-delete li .separator").html(", "),k("#pending-menu-items-to-delete li .separator").last().html(".")})},setBulkDeleteCheckboxStatus:function(){var e=k("#menu-to-edit .menu-item-checkbox");k.each(e,function(){k(this).prop("disabled")?k(this).prop("disabled",!1):k(this).prop("disabled",!0),k(this).is(":checked")&&k(this).prop("checked",!1)}),this.setRemoveSelectedButtonStatus()},setRemoveSelectedButtonStatus:function(){var e=k(".menu-items-delete");0<k(".menu-item-checkbox:checked").length?e.removeClass("disabled"):e.addClass("disabled")},attachMenuSaveSubmitListeners:function(){k("#update-nav-menu").on("submit",function(){var e=k("#update-nav-menu").serializeArray();k('[name="nav-menu-data"]').val(JSON.stringify(e))})},attachThemeLocationsListeners:function(){var e=k("#nav-menu-theme-locations"),t={action:"menu-locations-save"};t["menu-settings-column-nonce"]=k("#menu-settings-column-nonce").val(),e.find('input[type="submit"]').on("click",function(){return e.find("select").each(function(){t[this.name]=k(this).val()}),e.find(".spinner").addClass("is-active"),k.post(ajaxurl,t,function(){e.find(".spinner").removeClass("is-active")}),!1})},attachQuickSearchListeners:function(){var t;k("#nav-menu-meta").on("submit",function(e){e.preventDefault()}),k("#nav-menu-meta").on("input",".quick-search",function(){var e=k(this);e.attr("autocomplete","off"),t&&clearTimeout(t),t=setTimeout(function(){I.updateQuickSearchResults(e)},500)}).on("blur",".quick-search",function(){I.lastSearch=""})},updateQuickSearchResults:function(e){var t,n,i=e.val();i.length<2||I.lastSearch==i||(I.lastSearch=i,t=e.parents(".tabs-panel"),n={action:"menu-quick-search","response-format":"markup",menu:k("#menu").val(),"menu-settings-column-nonce":k("#menu-settings-column-nonce").val(),q:i,type:e.attr("name")},k(".spinner",t).addClass("is-active"),k.post(ajaxurl,n,function(e){I.processQuickSearchQueryResponse(e,n,t)}))},addCustomLink:function(e){var t=k("#custom-menu-item-url").val().toString(),n=k("#custom-menu-item-name").val();if(""!==t&&(t=t.trim()),e=e||I.addMenuItemToBottom,""===t||"https://"==t||"http://"==t)return k("#customlinkdiv").addClass("form-invalid"),!1;k(".customlinkdiv .spinner").addClass("is-active"),this.addLinkToMenu(t,n,e,function(){k(".customlinkdiv .spinner").removeClass("is-active"),k("#custom-menu-item-name").val("").trigger("blur"),k("#custom-menu-item-url").val("").attr("placeholder","https://")})},addLinkToMenu:function(e,t,n,i){n=n||I.addMenuItemToBottom,I.addItemToMenu({"-1":{"menu-item-type":"custom","menu-item-url":e,"menu-item-title":t}},n,i=i||function(){})},addItemToMenu:function(e,n,i){var a,t=k("#menu").val(),s=k("#menu-settings-column-nonce").val();n=n||function(){},i=i||function(){},a={action:"add-menu-item",menu:t,"menu-settings-column-nonce":s,"menu-item":e},k.post(ajaxurl,a,function(e){var t=k("#menu-instructions");e=(e=e||"").toString().trim(),n(e,a),k("li.pending").hide().fadeIn("slow"),k(".drag-instructions").show(),!t.hasClass("menu-instructions-inactive")&&t.siblings().length&&t.addClass("menu-instructions-inactive"),i()})},addMenuItemToBottom:function(e){e=k(e);e.hideAdvancedMenuItemFields().appendTo(I.targetList),I.refreshKeyboardAccessibility(),I.refreshAdvancedAccessibility(),wp.a11y.speak(menus.itemAdded),k(document).trigger("menu-item-added",[e])},addMenuItemToTop:function(e){e=k(e);e.hideAdvancedMenuItemFields().prependTo(I.targetList),I.refreshKeyboardAccessibility(),I.refreshAdvancedAccessibility(),wp.a11y.speak(menus.itemAdded),k(document).trigger("menu-item-added",[e])},attachUnsavedChangesListener:function(){k("#menu-management input, #menu-management select, #menu-management, #menu-management textarea, .menu-location-menus select").on("change",function(){I.registerChange()}),0!==k("#menu-to-edit").length||0!==k(".menu-location-menus select").length?window.onbeforeunload=function(){if(I.menusChanged)return wp.i18n.__("The changes you made will be lost if you navigate away from this page.")}:k("#menu-settings-column").find("input,select").end().find("a").attr("href","#").off("click")},registerChange:function(){I.menusChanged=!0},attachTabsPanelListeners:function(){k("#menu-settings-column").on("click",function(e){var t,n,i,a,s=k(e.target);if(s.hasClass("nav-tab-link"))n=s.data("type"),i=s.parents(".accordion-section-content").first(),k("input",i).prop("checked",!1),k(".tabs-panel-active",i).removeClass("tabs-panel-active").addClass("tabs-panel-inactive"),k("#"+n,i).removeClass("tabs-panel-inactive").addClass("tabs-panel-active"),k(".tabs",i).removeClass("tabs"),s.parent().addClass("tabs"),k(".quick-search",i).trigger("focus"),i.find(".tabs-panel-active .menu-item-title").length?i.removeClass("has-no-menu-item"):i.addClass("has-no-menu-item"),e.preventDefault();else if(s.hasClass("select-all"))(t=s.closest(".button-controls").data("items-type"))&&((a=k("#"+t+" .tabs-panel-active .menu-item-title input")).length!==a.filter(":checked").length||s.is(":checked")?s.is(":checked")&&a.prop("checked",!0):a.prop("checked",!1));else if(s.hasClass("menu-item-checkbox"))(t=s.closest(".tabs-panel-active").parent().attr("id"))&&(a=k("#"+t+" .tabs-panel-active .menu-item-title input"),n=k('.button-controls[data-items-type="'+t+'"] .select-all'),a.length!==a.filter(":checked").length||n.is(":checked")?n.is(":checked")&&n.prop("checked",!1):n.prop("checked",!0));else if(s.hasClass("submit-add-to-menu"))return I.registerChange(),e.target.id&&"submit-customlinkdiv"==e.target.id?I.addCustomLink(I.addMenuItemToBottom):e.target.id&&-1!=e.target.id.indexOf("submit-")&&k("#"+e.target.id.replace(/submit-/,"")).addSelectedToMenu(I.addMenuItemToBottom),!1}),k("#nav-menu-meta").on("click","a.page-numbers",function(){var n=k(this).closest(".inside");return k.post(ajaxurl,this.href.replace(/.*\?/,"").replace(/action=([^&]*)/,"")+"&action=menu-get-metabox",function(e){var t=JSON.parse(e);-1!==e.indexOf("replace-id")&&(e=document.getElementById(t["replace-id"]),t.markup)&&e&&n.html(t.markup)}),!1})},eventOnClickEditLink:function(e){var t,e=/#(.*)$/.exec(e.href);if(e&&e[1]&&0!==(t=(e=k("#"+e[1])).parent()).length)return t.hasClass("menu-item-edit-inactive")?(e.data("menu-item-data")||e.data("menu-item-data",e.getItemData()),e.slideDown("fast"),t.removeClass("menu-item-edit-inactive").addClass("menu-item-edit-active")):(e.slideUp("fast"),t.removeClass("menu-item-edit-active").addClass("menu-item-edit-inactive")),!1},eventOnClickCancelLink:function(e){var t=k(e).closest(".menu-item-settings"),e=k(e).closest(".menu-item");return e.removeClass("menu-item-edit-active").addClass("menu-item-edit-inactive"),t.setItemData(t.data("menu-item-data")).hide(),e.find(".menu-item-title").text(t.data("menu-item-data")["menu-item-title"]),!1},eventOnClickMenuSave:function(){var e="",t=k("#menu-name"),n=t.val();return n&&n.replace(/\s+/,"")?(k("#nav-menu-theme-locations select").each(function(){e+='<input type="hidden" name="'+this.name+'" value="'+k(this).val()+'" />'}),k("#update-nav-menu").append(e),I.menuList.find(".menu-item-data-position").val(function(e){return e+1}),!(window.onbeforeunload=null)):(t.parent().addClass("form-invalid"),!1)},eventOnClickMenuDelete:function(){return!!window.confirm(wp.i18n.__("You are about to permanently delete this menu.\n'Cancel' to stop, 'OK' to delete."))&&!(window.onbeforeunload=null)},eventOnClickMenuItemDelete:function(e){e=parseInt(e.id.replace("delete-",""),10);return I.removeMenuItem(k("#menu-item-"+e)),I.registerChange(),!1},processQuickSearchQueryResponse:function(e,t,n){var i,a,s,m={},o=document.getElementById("nav-menu-meta"),u=/menu-item[(\[^]\]*/,e=k("<div>").html(e).find("li"),d=n.closest(".accordion-section-content"),l=d.find(".button-controls .select-all");e.length?(e.each(function(){if(s=k(this),(i=u.exec(s.html()))&&i[1]){for(a=i[1];o.elements["menu-item["+a+"][menu-item-type]"]||m[a];)a--;m[a]=!0,a!=i[1]&&s.html(s.html().replace(new RegExp("menu-item\\["+i[1]+"\\]","g"),"menu-item["+a+"]"))}}),k(".categorychecklist",n).html(e),k(".spinner",n).removeClass("is-active"),d.removeClass("has-no-menu-item"),l.is(":checked")&&l.prop("checked",!1)):(k(".categorychecklist",n).html("<li><p>"+wp.i18n.__("No results found.")+"</p></li>"),k(".spinner",n).removeClass("is-active"),d.addClass("has-no-menu-item"))},removeMenuItem:function(t){var n=t.childMenuItems();k(document).trigger("menu-removing-item",[t]),t.addClass("deleting").animate({opacity:0,height:0},350,function(){var e=k("#menu-instructions");t.remove(),n.shiftDepthClass(-1).updateParentMenuItemDBId(),0===k("#menu-to-edit li").length&&(k(".drag-instructions").hide(),e.removeClass("menu-instructions-inactive")),I.refreshAdvancedAccessibility(),wp.a11y.speak(menus.itemRemoved),menus.updateParentDropdown(),menus.updateOrderDropdown()})},depthToPx:function(e){return e*I.options.menuItemDepthPerLevel},pxToDepth:function(e){return Math.floor(e/I.options.menuItemDepthPerLevel)}};k(function(){wpNavMenu.init(),k(".menu-edit a, .menu-edit button, .menu-edit input, .menu-edit textarea, .menu-edit select").on("focus",function(){var e,t,n;783<=window.innerWidth&&(e=k("#nav-menu-footer").height()+20,0<(t=k(this).offset().top-(k(window).scrollTop()+k(window).height()-k(this).height()))&&(t=0),(t*=-1)<e)&&(n=k(document).scrollTop(),k(document).scrollTop(n+(e-t)))})}),k(document).on("menu-item-added",function(){k(".bulk-actions").is(":visible")||k(".bulk-actions").show()}),k(document).on("menu-removing-item",function(e,t){1===k(t).parents("#menu-to-edit").find("li").length&&k(".bulk-actions").is(":visible")&&k(".bulk-actions").hide()})}(jQuery);