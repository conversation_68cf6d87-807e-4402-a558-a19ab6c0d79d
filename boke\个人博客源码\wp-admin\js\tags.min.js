/*! This file is auto-generated */
jQuery(function(s){var o=!1;s("#the-list").on("click",".delete-tag",function(){var t,e=s(this),n=e.parents("tr"),a=!0;return(a="undefined"!=showNotice?showNotice.warn():a)&&(t=e.attr("href").replace(/[^?]*\?/,"").replace(/action=delete/,"action=delete-tag"),s.post(ajaxurl,t,function(e){"1"==e?(s("#ajax-response").empty(),n.fadeOut("normal",function(){n.remove()}),s('select#parent option[value="'+t.match(/tag_ID=(\d+)/)[1]+'"]').remove(),s("a.tag-link-"+t.match(/tag_ID=(\d+)/)[1]).remove()):("-1"==e?s("#ajax-response").empty().append('<div class="error"><p>'+wp.i18n.__("Sorry, you are not allowed to do that.")+"</p></div>"):s("#ajax-response").empty().append('<div class="error"><p>'+wp.i18n.__("Something went wrong.")+"</p></div>"),n.children().css("backgroundColor",""))}),n.children().css("backgroundColor","#f33")),!1}),s("#edittag").on("click",".delete",function(e){if("undefined"==typeof showNotice)return!0;showNotice.warn()||e.preventDefault()}),s("#submit").on("click",function(){var r=s(this).parents("form");return o||(o=!0,r.find(".submit .spinner").addClass("is-active"),s.post(ajaxurl,s("#addtag").serialize(),function(e){var t,n,a;if(o=!1,r.find(".submit .spinner").removeClass("is-active"),s("#ajax-response").empty(),(t=wpAjax.parseAjaxResponse(e,"ajax-response")).errors&&"empty_term_name"===t.responses[0].errors[0].code&&validateForm(r),t&&!t.errors){if(0<(e=r.find("select#parent").val())&&0<s("#tag-"+e).length?s(".tags #tag-"+e).after(t.responses[0].supplemental.noparents):s(".tags").prepend(t.responses[0].supplemental.parents),s(".tags .no-items").remove(),r.find("select#parent")){for(e=t.responses[1].supplemental,n="",a=0;a<t.responses[1].position;a++)n+="&nbsp;&nbsp;&nbsp;";r.find("select#parent option:selected").after('<option value="'+e.term_id+'">'+n+e.name+"</option>")}s('input:not([type="checkbox"]):not([type="radio"]):not([type="button"]):not([type="submit"]):not([type="reset"]):visible, textarea:visible',r).val("")}})),!1})});