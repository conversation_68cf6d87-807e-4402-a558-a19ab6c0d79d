jQuery(document).ready(function($) {
    // 头像预览功能
    $('#avatar').on('input', function() {
        var avatarUrl = $(this).val();
        var preview = $('#avatar-preview');
        
        if (preview.length === 0) {
            $(this).after('<div id="avatar-preview" style="margin-top: 10px;"></div>');
            preview = $('#avatar-preview');
        }
        
        if (avatarUrl) {
            preview.html('<img src="' + avatarUrl + '" style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover;" alt="头像预览">');
        } else {
            preview.empty();
        }
    });
    
    // 触发初始预览
    $('#avatar').trigger('input');
    
    // URL验证
    $('#url').on('blur', function() {
        var url = $(this).val();
        if (url && !url.match(/^https?:\/\/.+/)) {
            alert('请输入有效的网址，以 http:// 或 https:// 开头');
            $(this).focus();
        }
    });
    
    // 表单验证
    $('form').on('submit', function(e) {
        var name = $('#name').val().trim();
        var url = $('#url').val().trim();
        
        if (!name) {
            alert('请输入友链名称');
            $('#name').focus();
            e.preventDefault();
            return false;
        }
        
        if (!url) {
            alert('请输入友链地址');
            $('#url').focus();
            e.preventDefault();
            return false;
        }
        
        if (!url.match(/^https?:\/\/.+/)) {
            alert('请输入有效的网址');
            $('#url').focus();
            e.preventDefault();
            return false;
        }
    });
    
    // 批量操作
    $('#bulk-action-selector-top, #bulk-action-selector-bottom').on('change', function() {
        var action = $(this).val();
        var button = $(this).siblings('.button');
        
        if (action === 'delete') {
            button.addClass('button-danger');
        } else {
            button.removeClass('button-danger');
        }
    });
    
    // 确认删除
    $('.delete-link').on('click', function(e) {
        if (!confirm('确定要删除这个友链吗？此操作不可恢复。')) {
            e.preventDefault();
            return false;
        }
    });
    
    // 排序功能（简单版）
    $('.sort-input').on('change', function() {
        var $this = $(this);
        var newOrder = parseInt($this.val());
        var linkId = $this.data('link-id');
        
        // 这里可以添加AJAX请求来更新排序
        // 暂时只是视觉反馈
        $this.closest('tr').css('background-color', '#fff3cd');
        setTimeout(function() {
            $this.closest('tr').css('background-color', '');
        }, 1000);
    });
    
    // 状态切换动画
    $('.status-toggle').on('click', function(e) {
        var $this = $(this);
        $this.closest('td').find('.status-indicator').addClass('updating');
    });
    
    // 添加一些视觉增强
    $('.wp-list-table tbody tr').hover(
        function() {
            $(this).addClass('hover');
        },
        function() {
            $(this).removeClass('hover');
        }
    );
});
