/*! This file is auto-generated */
!function(t){window.findPosts={open:function(n,e){var i=t(".ui-find-overlay");return 0===i.length&&(t("body").append('<div class="ui-find-overlay"></div>'),findPosts.overlay()),i.show(),n&&e&&t("#affected").attr("name",n).val(e),t("#find-posts").show(),t("#find-posts-input").trigger("focus").on("keyup",function(n){27==n.which&&findPosts.close()}),findPosts.send(),!1},close:function(){t("#find-posts-response").empty(),t("#find-posts").hide(),t(".ui-find-overlay").hide()},overlay:function(){t(".ui-find-overlay").on("click",function(){findPosts.close()})},send:function(){var n={ps:t("#find-posts-input").val(),action:"find_posts",_ajax_nonce:t("#_ajax_nonce").val()},e=t(".find-box-search .spinner");e.addClass("is-active"),t.ajax(ajaxurl,{type:"POST",data:n,dataType:"json"}).always(function(){e.removeClass("is-active")}).done(function(n){n.success||t("#find-posts-response").text(wp.i18n.__("An error has occurred. Please reload the page and try again.")),t("#find-posts-response").html(n.data)}).fail(function(){t("#find-posts-response").text(wp.i18n.__("An error has occurred. Please reload the page and try again."))})}},t(function(){var o,n,e=t("#wp-media-grid"),i=new ClipboardJS(".copy-attachment-url.media-library"),s=null;e.length&&window.wp&&window.wp.media&&(n=_wpMediaGridSettings,n=window.wp.media({frame:"manage",container:e,library:n.queryVars}).open(),e.trigger("wp-media-grid-ready",n)),t("#find-posts-submit").on("click",function(n){t('#find-posts-response input[type="radio"]:checked').length||n.preventDefault()}),t("#find-posts .find-box-search :input").on("keypress",function(n){if(13==n.which)return findPosts.send(),!1}),t("#find-posts-search").on("click",findPosts.send),t("#find-posts-close").on("click",findPosts.close),t("#doaction").on("click",function(e){t('select[name="action"]').each(function(){var n=t(this).val();"attach"===n?(e.preventDefault(),findPosts.open()):"delete"!==n||showNotice.warn()||e.preventDefault()})}),t(".find-box-inside").on("click","tr",function(){t(this).find(".found-radio input").prop("checked",!0)}),i.on("success",function(n){var e=t(n.trigger),i=t(".success",e.closest(".copy-to-clipboard-container"));n.clearSelection(),s&&s.addClass("hidden"),clearTimeout(o),i.removeClass("hidden"),o=setTimeout(function(){i.addClass("hidden"),s=null},3e3),s=i,wp.a11y.speak(wp.i18n.__("The file URL has been copied to your clipboard"))})})}(jQuery);