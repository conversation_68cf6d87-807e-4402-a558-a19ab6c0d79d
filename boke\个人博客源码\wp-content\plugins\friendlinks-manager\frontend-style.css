/* 友链前端显示样式 */

.friendlinks-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.friendlink-card {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.friendlink-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #007cba;
}

.friendlink-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #007cba, #00a0d2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.friendlink-card:hover::before {
    opacity: 1;
}

.friendlink-avatar {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    background: #f0f0f1;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #e1e5e9;
    transition: border-color 0.3s ease;
}

.friendlink-card:hover .friendlink-avatar {
    border-color: #007cba;
}

.friendlink-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.friendlink-avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: #666;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-transform: uppercase;
}

.friendlink-content {
    flex: 1;
    min-width: 0;
}

.friendlink-name {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    line-height: 1.3;
}

.friendlink-name a {
    color: #1e1e1e;
    text-decoration: none;
    transition: color 0.3s ease;
}

.friendlink-name a:hover {
    color: #007cba;
}

.friendlink-description {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .friendlink-card {
        background: #1e1e1e;
        border-color: #333;
        color: #fff;
    }
    
    .friendlink-card:hover {
        border-color: #00a0d2;
    }
    
    .friendlink-name a {
        color: #fff;
    }
    
    .friendlink-name a:hover {
        color: #00a0d2;
    }
    
    .friendlink-description {
        color: #ccc;
    }
    
    .friendlink-avatar {
        border-color: #333;
        background: #2c2c2c;
    }
    
    .friendlink-card:hover .friendlink-avatar {
        border-color: #00a0d2;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .friendlinks-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .friendlink-card {
        padding: 15px;
        gap: 12px;
    }
    
    .friendlink-avatar {
        width: 50px;
        height: 50px;
    }
    
    .friendlink-name {
        font-size: 16px;
    }
    
    .friendlink-description {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .friendlink-card {
        padding: 12px;
        gap: 10px;
    }
    
    .friendlink-avatar {
        width: 45px;
        height: 45px;
    }
    
    .friendlink-avatar-placeholder {
        font-size: 18px;
    }
}

/* 特殊效果 */
.friendlink-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 为不同位置的卡片添加延迟动画 */
.friendlink-card:nth-child(1) { animation-delay: 0.1s; }
.friendlink-card:nth-child(2) { animation-delay: 0.2s; }
.friendlink-card:nth-child(3) { animation-delay: 0.3s; }
.friendlink-card:nth-child(4) { animation-delay: 0.4s; }
.friendlink-card:nth-child(5) { animation-delay: 0.5s; }
.friendlink-card:nth-child(6) { animation-delay: 0.6s; }

/* 加载状态 */
.friendlinks-loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.friendlinks-empty {
    text-align: center;
    padding: 40px;
    color: #999;
    font-style: italic;
}
