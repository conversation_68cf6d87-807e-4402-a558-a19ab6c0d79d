/* 友链管理后台样式 */

.friendlinks-admin {
    max-width: 1200px;
}

.friendlinks-admin .form-table th {
    width: 150px;
}

.friendlinks-admin .form-table td {
    padding: 15px 10px;
}

.status-active {
    color: #46b450;
    font-weight: bold;
}

.status-inactive {
    color: #dc3232;
    font-weight: bold;
}

.wp-list-table .column-avatar {
    width: 60px;
}

.wp-list-table .column-name {
    width: 150px;
}

.wp-list-table .column-url {
    width: 200px;
}

.wp-list-table .column-description {
    width: 250px;
}

.wp-list-table .column-status {
    width: 80px;
}

.wp-list-table .column-sort {
    width: 60px;
}

.wp-list-table .column-actions {
    width: 150px;
}

.wp-list-table img {
    border-radius: 50%;
    object-fit: cover;
}

.notice {
    margin: 5px 0 15px;
}

.form-table input[type="text"],
.form-table input[type="url"],
.form-table input[type="number"],
.form-table textarea {
    width: 100%;
    max-width: 400px;
}

.form-table textarea {
    height: 80px;
    resize: vertical;
}

.description {
    color: #666;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .wp-list-table .column-description {
        display: none;
    }
    
    .wp-list-table .column-url {
        width: auto;
    }
}
